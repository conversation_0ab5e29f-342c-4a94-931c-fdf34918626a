package com.jykj.dqm.config;

import com.jykj.dqm.metadata.dao.MetadataTaskInstanceMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.system.dao.LogLoginMapper;
import com.jykj.dqm.system.dao.OperLogMapper;
import com.jykj.dqm.system.dao.SysConfigMapper;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 14:50
 */
@Slf4j
@Configuration
@EnableScheduling
public class ScheduleTask {
    @Autowired
    private OperLogMapper operLogMapper;

    @Autowired
    private LogLoginMapper logLoginMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Autowired
    private MetadataTaskInstanceMapper metadataTaskInstanceMapper;


    //每天1点执行，日志清理
    @Scheduled(cron = "0 0 1 * * ?")
    void configureLogTasks() {
        try {
            //清理日志
            log.info("开始执行清理日志定时任务" + LocalDateTime.now());
            List<SysConfig> sysConfigs = sysConfigMapper.querySysConfigAll();
            String flag = "N";
            String day = "15";
            for (SysConfig sysConfig : sysConfigs) {
                if ("enable.periodic.log.clearing".equals(sysConfig.getConfigCode())) {
                    flag = sysConfig.getConfigValue();
                }
                if ("config.log.retention.time".equals(sysConfig.getConfigCode())) {
                    day = sysConfig.getConfigValue();
                }
            }
            if ("N".equalsIgnoreCase(flag)) {
                log.info("未开启清理日志定时任务");
                return;
            }
            int dayNum = Integer.parseInt(day);
            LocalDate now = LocalDate.now();
            LocalDate afterNow = now.minusDays(dayNum);
            Date date = DateTimeUtil.localDate2Date(afterNow);
            //清除操作日志
            int num = operLogMapper.clearLog(date);
            log.info("--------清除操作日志{}条", num);
            //清除登录日志
            num = logLoginMapper.clearloginLog(date);
            log.info("--------清除登录日志{}条", num);
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("清除日志定时任务失败", e);
        }
    }

    //每小时检查一次，检核规则的超时任务标记失败
    @Scheduled(fixedRate = 1 * 60 * 60 * 1000)
    void configureDataqulityTasks() {
        try {
            log.info("开始更新检核规则超时的任务" + LocalDateTime.now());
            //dqm_dataquality_task_instance 表正在执行中的任务判断，如果执行超过1小时，直接标记失败
            int num = dataqualityTaskInstanceMapper.updateTaskState();
            log.info("--------更新dqm_dataquality_task_instance超时的任务{}条", num);
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("更新检核超时任务失败", e);
        }
    }

    //每小时检查一次，数源的超时任务标记失败
    @Scheduled(fixedRate = 1 * 60 * 60 * 1000)
    void configureMetedataTasks() {
        try {
            log.info("开始更新获取数源超时的任务" + LocalDateTime.now());
            //dqm_dataquality_task_instance 表正在执行中的任务判断，如果执行超过1小时，直接标记失败
            int num = metadataTaskInstanceMapper.updateTaskState();
            log.info("--------更新dqm_metadata_task_instance超时的任务{}条", num);
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("更新获取数源超时任务失败", e);
        }
    }
}
