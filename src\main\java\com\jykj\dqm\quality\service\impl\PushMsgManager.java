package com.jykj.dqm.quality.service.impl;

import cn.hutool.extra.mail.MailUtil;
import com.jykj.dqm.notice.sms.MyAliyunSms;
import com.jykj.dqm.quality.dao.NoticeSendLogMapper;
import com.jykj.dqm.quality.entity.NoticeSendLog;
import com.jykj.dqm.quality.entity.PushMsgReceiver;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 发送通知消息
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/11 14:18:15
 */
@Slf4j
@Service
public class PushMsgManager {
    @Autowired
    private NoticeSendLogMapper noticeSendLogMapper;

    /**
     * 发送通知消息
     *
     * @param pushMessageReceivers 接收方
     * @param msg                  消息内容
     * @param qstnId               问题ID
     * <AUTHOR>
     */
    @Async
    public void pushMsg(List<PushMsgReceiver> pushMessageReceivers, String msg, String qstnId) {
        List<String> mailList = new ArrayList<>();
        NoticeSendLog noticeSendLog = NoticeSendLog.builder()
                .noticeMsg(msg)
                .dataQltyQstnId(qstnId)
                .noticeTime(new Date())
                .build();
        String account;
        String accountType;
        for (PushMsgReceiver pushMessageReceiver : pushMessageReceivers) {
            account = pushMessageReceiver.getAccount();
            accountType = pushMessageReceiver.getAccountType();
            if ("1".equals(accountType)) {
                //短信
                try {
                    //发送短信
                    SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
                    MyAliyunSms.sendSms(msg, account);

                    noticeSendLog.setAccount(account);
                    noticeSendLog.setAccountType(accountType);
                    noticeSendLog.setNoticeResult("短信通知成功");
                    log.info("------短信发送成功！" + account);
                } catch (Exception e) {
                    noticeSendLog.setNoticeResult("短信通知失败:" + StringUtil.getErrorMsg(e));
                    log.info("------短信发送失败！" + account + ":" + e.getMessage());
                } finally {
                    noticeSendLogMapper.insertSelective(noticeSendLog);
                }
            } else if ("0".equals(accountType)) {
                //邮件组合发送
                mailList.add(account);
            }
        }
        //邮件组合发送
        if (mailList.size() > 0) {
            try {
                MailUtil.send(mailList, "数据质量问题", msg, false);
                noticeSendLog.setAccount(mailList.toString());
                noticeSendLog.setAccountType("0");
                noticeSendLog.setNoticeResult("邮件通知成功");
                log.info("------邮件发送成功！" + String.join(",", mailList));
            } catch (Exception e) {
                noticeSendLog.setNoticeResult("邮件通知失败:" + StringUtil.getErrorMsg(e));
                log.info("------邮件发送失败！" + String.join(",", mailList) + ":" + e.getMessage());
            } finally {
                noticeSendLogMapper.insertSelective(noticeSendLog);
            }
        }

    }
}
