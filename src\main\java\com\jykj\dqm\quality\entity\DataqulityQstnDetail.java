package com.jykj.dqm.quality.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 检核错误详情记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 10:21:16
 */
@ApiModel(value = "检核错误详情记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQULITY_QSTN_DETAIL")
public class DataqulityQstnDetail implements Serializable {
    /**
     * 明细ID
     */
    @TableId(value = "DETAIL_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "明细ID")
    private Integer detailId;

    /**
     * 问题ID
     */
    @TableField(value = "DATA_QLTY_QSTN_ID")
    @ApiModelProperty(value = "问题ID")
    private String dataQltyQstnId;

    /**
     * 规则ID
     */
    @TableField(value = "CHECK_RULE_ID")
    @ApiModelProperty(value = "规则ID")
    private String checkRuleId;

    /**
     * 明细行
     */
    @TableField(value = "DETAIL_LINE")
    @ApiModelProperty(value = "明细行")
    private String detailLine;

    /**
     * 明细行哈希值（用于去重查询，解决Oracle CLOB字段DISTINCT问题）
     * 自动填充：插入和更新时根据DETAIL_LINE自动生成
     */
    @TableField(value = "DETAIL_LINE_HASH", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "明细行哈希值")
    private String detailLineHash;

    /**
     * 明细行
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "明细行JSON")
    private JSON detailLineJson;

    /**
     * 预留字段1
     */
    @TableField(value = "RESERVED_FIELD1")
    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;

    /**
     * 预留字段2
     */
    @TableField(value = "RESERVED_FIELD2")
    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    /**
     * 预留字段3
     */
    @TableField(value = "RESERVED_FIELD3")
    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    private static final long serialVersionUID = 1L;
}