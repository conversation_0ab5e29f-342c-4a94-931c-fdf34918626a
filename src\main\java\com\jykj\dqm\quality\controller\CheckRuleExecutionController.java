package com.jykj.dqm.quality.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.quality.entity.CheckRuleExecutionDTO;
import com.jykj.dqm.quality.entity.CheckRuleExecutionQueryDTO;
import com.jykj.dqm.quality.entity.CheckRuleTableOrFieldQueryDTO;
import com.jykj.dqm.quality.entity.QCTaskDTO;
import com.jykj.dqm.quality.service.CheckRuleExecutionService;
import com.jykj.dqm.quartz.entity.CronExpressionItem;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.scheduler.CronExpressionUtil;
import com.jykj.dqm.utils.MapperUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 规则执行
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/26 15:53:37
 */
@Api(tags = {"规则执行"})
@RestController
@RequestMapping("/ruleExecution")
public class CheckRuleExecutionController {
    public static Map<String, CronExpression> cronExpressionMap = Maps.newConcurrentMap();

    @Autowired
    private CheckRuleExecutionService checkRuleExecutionService;

    @ApiOperation(value = "获取所有规则的系统和数据库", notes = "规则执行")
    @GetMapping(value = "/getAllSystem")
    public R getAllSystem() {
        return checkRuleExecutionService.getAllSystem();
    }

    @LogRemark(operate = "规则执行设置", module = "规则执行")
    @ApiOperation(value = "规则执行设置(先设置是否是整体执行（0：单独设置 1：整体设置），如果是整体，还需要设置是否覆盖（是否覆盖：0：不覆盖 1：覆盖）)", notes = "规则执行")
    @PostMapping("/configRule")
    public R configRule(@RequestBody QCTaskDTO qcTaskDTO) {
        return checkRuleExecutionService.configRule(qcTaskDTO);
    }

    @SneakyThrows
    @PostMapping(value = "/genCronByJobInfo")
    public R genCronByJobInfo(@RequestBody QCTaskDTO qcTaskDTO) {
        ScheduleJobInfo job = MapperUtils.INSTANCE.map(ScheduleJobInfo.class, qcTaskDTO);
        ScheduleJobInfo scheduleJobInfo = CronExpressionUtil.pageValue2CronValue(job);
        String cron = scheduleJobInfo.getCronExpression();
        CronExpression cronExpression = cronExpressionMap.get(cron);
        if (cronExpression == null) {
            cronExpression = new CronExpression(cron);
            cronExpressionMap.put(cron, cronExpression);
        }
        CronExpressionItem cronStr = new CronExpressionItem();
        cronStr.setCronExpression(cron);
        cronStr.setCronDesc(scheduleJobInfo.getJobDesc());
        int MAX_COUNT_NEXT_TIME = 10;
        List<String> nextTimeList = new ArrayList<>(10);
        Date lastTime = new Date();
        for (int i = 0; i < MAX_COUNT_NEXT_TIME; ) {
            lastTime = cronExpression.getNextValidTimeAfter(lastTime);
            if (lastTime != null) {
                nextTimeList.add(DateUtil.formatDateTime(lastTime));
                i++;
            }
        }
        cronStr.setCronExecTimes(nextTimeList);
        return RUtil.success(cronStr);
    }

    @ApiOperation(value = "根据系统，以及其他条件查询对应的规则", notes = "规则执行")
    @PostMapping(value = "/ruleRunInfoList")
    public R ruleRunInfoList(@Validated @RequestBody CheckRuleExecutionQueryDTO checkRuleExecutionQueryDTO) {
        return checkRuleExecutionService.ruleRunInfoList(checkRuleExecutionQueryDTO);
    }

    @LogRemark(operate = "执行当前系统下所有规则，结果使用返回的ID获取", module = "规则执行")
    @ApiOperation(value = "执行当前系统下所有规则", notes = "规则执行")
    @PostMapping(value = "/exeAllRuleBySys")
    public R exeAllRuleBySys(@Validated @RequestBody CheckRuleExecutionDTO checkRuleExecutionDTO) {
        return checkRuleExecutionService.exeAllRuleBySys(checkRuleExecutionDTO);
    }

    @LogRemark(operate = "执行一条规则，结果使用返回的ID获取", module = "规则执行")
    @ApiOperation(value = "执行一条规则", notes = "规则执行")
    @PostMapping(value = "/exeOneRule")
    public R exeOneRule(@Validated @RequestBody CheckRuleExecutionDTO checkRuleExecutionDTO) {
        return checkRuleExecutionService.exeOneRule(checkRuleExecutionDTO);
    }

    @LogRemark(operate = "获取进度及结果", module = "规则执行")
    @ApiOperation(value = "获取进度及结果", notes = "规则执行")
    @PostMapping(value = "/getProgress")
    public R getProgress(@RequestParam("groupId") String groupId) {
        return checkRuleExecutionService.getProgress(groupId);
    }

    @ApiOperation(value = "获取当前系统和数据库的表（sysCode、dbNm不能为空）", notes = "规则配置")
    @PostMapping(value = "/getTable")
    public R getTable(@Validated @RequestBody CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO) {
        return checkRuleExecutionService.getTable(checkRuleTableOrFieldQueryDTO);
    }

    @ApiOperation(value = "获取表对应的字段(sysCode、dbNm和tableName不能为空)", notes = "规则配置")
    @PostMapping(value = "/getTableField")
    public R getTableField(@Validated @RequestBody CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO) {
        return checkRuleExecutionService.getTableField(checkRuleTableOrFieldQueryDTO);
    }
}
