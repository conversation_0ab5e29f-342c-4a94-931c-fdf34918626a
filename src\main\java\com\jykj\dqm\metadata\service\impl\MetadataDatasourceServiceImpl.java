package com.jykj.dqm.metadata.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.common.UploadDriverFile;
import com.jykj.dqm.utils.DbQueryNewUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.dao.TaskGroupSubtasksMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceDeleteDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceQueryDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceVO;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResult;
import com.jykj.dqm.metadata.entity.MetadataStructureDetail;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasks;
import com.jykj.dqm.metadata.service.MetadataDatasourceService;
import com.jykj.dqm.metadata.service.MetadataStructureChooseResultService;
import com.jykj.dqm.metadata.service.MetadataStructureDetailService;
import com.jykj.dqm.metadata.service.MetadataStructureInfoService;
import com.jykj.dqm.metadata.service.TaskGroupSubtasksService;
import com.jykj.dqm.utils.AESUtils;
import com.jykj.dqm.utils.PageUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.core.spring.SpringContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@Slf4j
@Service
public class MetadataDatasourceServiceImpl extends ServiceImpl<MetadataDatasourceMapper, MetadataDatasource> implements MetadataDatasourceService {
    @Autowired
    MetadataDatasourceMapper dqmMetadataDatasourceMapper;

    @Autowired
    MetadataStructureInfoService metadataStructureInfoService;

    @Autowired
    MetadataStructureDetailService metadataStructureDetailService;

    @Autowired
    TaskGroupSubtasksService dqmTaskGroupSubtasksService;

    @Autowired
    TaskGroupSubtasksMapper dqmTaskGroupSubtasksMapper;

    @Autowired
    UploadDriverFile uploadDriverFile;

    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Autowired
    private MetadataStructureChooseResultService metadataStructureChooseResultService;

    @Override
    public R selectMetadataDatasourceList(MetadataDatasourceQueryDTO dqmMetadataDatasource) {
        PageHelper.startPage(dqmMetadataDatasource.getPageNum(), dqmMetadataDatasource.getPageSize());
        //增加是否已关联任务选项 关联任务状态:0:全部；1：关联；2：未关联
        List<MetadataDatasource> dqmMetadataDatasources = dqmMetadataDatasourceMapper.getDatasourceByParams(dqmMetadataDatasource);
        PageInfo<MetadataDatasource> pageInfo = new PageInfo<>(dqmMetadataDatasources);
        PageInfo<MetadataDatasourceVO> copy = PageUtil.pageInfoCopy(pageInfo, MetadataDatasourceVO.class);
        for (MetadataDatasourceVO metadataDatasource : copy.getList()) {
            long count = metadataStructureChooseResultService.count(new QueryWrapper<MetadataStructureChooseResult>().eq("DATA_SOURCE_ID", metadataDatasource.getDataSourceId()));
            metadataDatasource.setConfigNum(count);
        }
        return RUtil.success(copy);
    }

    @Override
    public R addDatasource(MetadataDatasource dqmMetadataDatasource) {
        dqmMetadataDatasource.setOperationPerson("管理员");
        dqmMetadataDatasource.setDatabasePwd(AESUtils.AESEnCode(dqmMetadataDatasource.getDatabasePwd()));
        dqmMetadataDatasourceMapper.insert(dqmMetadataDatasource);
        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        return RUtil.success("新增成功！");
    }

    @Override
    public R updateDatasource(MetadataDatasource dqmMetadataDatasource) {
        dqmMetadataDatasource.setUpdateTime(new Date());
        //todo 查询时返回******，用这个判断
        if (!AESUtils.isAesEnCode(dqmMetadataDatasource.getDatabasePwd())) {
            dqmMetadataDatasource.setDatabasePwd(AESUtils.AESEnCode(dqmMetadataDatasource.getDatabasePwd()));
        }
        dqmMetadataDatasource.setUpdateTime(new Date());
        dqmMetadataDatasourceMapper.updateById(dqmMetadataDatasource);
        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        return RUtil.success("更新成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteDatasource(MetadataDatasourceDeleteDTO dqmMetadataDatasource) {
        List<MetadataDatasource> dqmMetadataDatasources = dqmMetadataDatasourceMapper.selectBatchIds(dqmMetadataDatasource.getDataSourceIds());
        dqmMetadataDatasourceMapper.deleteBatchIds(dqmMetadataDatasource.getDataSourceIds());
        //删除上传文件
        Integer datasourceId;
        for (MetadataDatasource metadataDatasource : dqmMetadataDatasources) {
            datasourceId = metadataDatasource.getDataSourceId();
            if (StrUtil.isNotBlank(metadataDatasource.getDriverFiles())) {
                FileUtil.del(SystemUtils.getFilePath() + "/" + metadataDatasource.getDriverFiles());
            }
            //删除采集的数据（根据数据源）
            metadataStructureInfoService.remove(new QueryWrapper<MetadataStructureInfo>().eq("DATA_SOURCE_ID", datasourceId));
            metadataStructureDetailService.remove(new QueryWrapper<MetadataStructureDetail>().eq("DATA_SOURCE_ID", datasourceId));
            //删除数据源，同步删除包含当前数据源的子任务
            dqmTaskGroupSubtasksService.remove(new QueryWrapper<TaskGroupSubtasks>().eq("SUB_TASK_ID", datasourceId));
        }
        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        return RUtil.success("删除成功！");
    }

    @Override
    public R testDb(MetadataDatasource dqmMetadataDatasource) {
        try {
            List<Map<String, Object>> result = dbQueryUtil.query(dqmMetadataDatasource, dqmMetadataDatasource.getTestsql());
            if (result != null) {
                return RUtil.success("测试连接成功！");
            } else {
                return RUtil.error("测试连接失败！");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RUtil.error("测试连接失败:" + e.getMessage());
        }
    }

    @Override
    public R uploadDriverFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return RUtil.error("文件不能为空！");
        }
        UploadDriverFile uploadDriverFile = SpringContextUtil.getBean(UploadDriverFile.class);
        double size = file.getSize() * 1.0 / 1024 / 1024;
        if (size > uploadDriverFile.getFileSize()) {
            return RUtil.error("文件大小不能超过10M！");
        }
        //获取上传文件的文件名
        String oldName = file.getOriginalFilename();
        String filenameExtension = StringUtils.getFilenameExtension(oldName);
        if (!uploadDriverFile.getFileTypes().contains(filenameExtension)) {
            return RUtil.error("文件类型不支持！");
        }

        try {
            String path = SystemUtils.getFilePath();
            if (!FileUtil.exist(path)) {
                FileUtil.mkdir(path);
            }
            //拼接成为新文件的路径
            String filePath = path + "/" + oldName;
            //创建新文件对象 指定文件路径为拼接好的路径
            File newFile = new File(filePath);
            //将前端传递过来的文件输送给新文件 这里需要抛出IO异常 throws IOException
            file.transferTo(newFile);
        } catch (IOException e) {
            throw new BusinessException("上传文件失败！" + e.getLocalizedMessage());
        }
        //上传完成后将文件路径返回给前端用作图片回显或增加时的文件路径值等
        return RUtil.success("上传成功！");
    }
}
