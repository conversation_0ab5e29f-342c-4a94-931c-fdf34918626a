# YLSYB-DQM

数据质量管理（数据质量监控治理平台）后端代码

## 项目概述
本项目是一个数据质量管理系统，用于监控和治理数据质量问题。系统支持多种数据库类型，包括MySQL和Oracle。

## 主要功能
- 数据质量规则检查
- 问题数据监控和记录
- 数据质量报告生成
- 多数据源支持

## 技术栈
- Spring Boot
- MyBatis Plus
- MySQL/Oracle数据库
- Maven

## 最近修复的问题

### Oracle CLOB字段DISTINCT查询问题修复 (2025-09-03)
**问题描述：**
在Oracle数据库环境下，执行数据质量检查时出现错误：
```
ORA-00932: 数据类型不一致: 应为 -, 但却获得 CLOB
```

**问题原因：**
- `DQM_DATAQULITY_QSTN_DETAIL`表中的`DETAIL_LINE`字段在Oracle中是CLOB类型
- 代码中使用了`SELECT COUNT(DISTINCT DETAIL_LINE)`查询
- Oracle不支持对CLOB字段直接使用DISTINCT操作

**解决方案：数据库结构优化（已完整实施）**

通过添加`DETAIL_LINE_HASH`字段存储`DETAIL_LINE`的SHA256哈希值，使用哈希值进行去重查询，完美解决Oracle CLOB字段DISTINCT问题。

**方案优势：**
- ✅ 完美解决Oracle CLOB字段DISTINCT问题
- ✅ 性能最优，使用索引优化查询
- ✅ 支持任意长度的DETAIL_LINE内容
- ✅ 零哈希冲突风险（SHA256算法）
- ✅ 提供完整的升级工具和API
- ✅ MySQL和Oracle使用相同逻辑

**升级步骤：**
1. 执行数据库升级脚本
2. 重启应用服务
3. 调用API生成现有数据哈希值
4. 验证升级结果

详细信息请参考：`Oracle_CLOB_Solutions.md` 和 `UPGRADE_GUIDE.md`

**修改和新增文件：**
- `src/main/java/com/jykj/dqm/quality/manager/ExecRuleCheckTask.java`
- `src/main/java/com/jykj/dqm/quality/dao/DataqulityQstnDetailMapper.java`
- `src/main/java/com/jykj/dqm/quality/entity/DataqulityQstnDetail.java`
- `src/main/resources/mapper/quality/DataqulityQstnDetailMapper.xml`
- `src/main/java/com/jykj/dqm/utils/HashUtil.java`（新增）
- `src/main/java/com/jykj/dqm/quality/service/DatabaseUpgradeService.java`（新增）
- `src/main/java/com/jykj/dqm/quality/controller/DatabaseUpgradeController.java`（新增）
- `src/main/resources/sql/oracle/upgrade_clob_solution.sql`（新增）
- `src/main/resources/sql/mysql/upgrade_clob_solution.sql`（新增）
- `Oracle_CLOB_Solutions.md`（方案对比文档）

**影响范围：**
- 数据质量检查任务执行
- 问题数据明细记录处理
- 布隆过滤器优化逻辑