server:
  tomcat:
    uri-encoding: UTF-8
    #最小线程数(最小工作线程数，默认10)
    min-spare-threads: 100
    #最大线程数（4核8g内存，线程数800，一般是核数*200。操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好）(最大工作线程数，默认200)
    max-threads: 800
    #最大链接数 （这个参数是指在同一时间，tomcat能够接受的最大连接数。一般这个值要大于(max-threads)+(accept-count)。）(最大连接数，默认为10000 )
    max-connections: 10000
    #最大等待队列长度(最大连接等待数，默认100)
    accept-count: 1000
    #请求头最大长度kb
    max-http-header-size: 102400
    #请请求体最大长度kb
    #max-http-post-size: 2097152
  port: 9090
  #链接建立超时时间
  request-timeout: 60s
  #connection-timeout: 12000
  maxHttpHeaderSize: 102400
  #  #Https证书配置
  ssl:
    key-store: classpath:keystore.p12
    key-store-password: Jykj1994
    key-store-type: PKCS12
    key-alias: tomcat
spring:
  cache:
    type: redis
  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 4
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    # password:
    # 连接超时时间（毫秒）
    timeout: 5000ms
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
  mvc:
    static-path-pattern=/static/**:
    # 配置策略
    matching-strategy: ant-path-matcher

  servlet:
    multipart:
      location: templates
      max-file-size: 20MB
      max-request-size: 50MB
  output:
    ansi:
      enabled: always
  logging:
    level:
      com.scjy.dqm.*.*mapper: DEBUG
      config: classpath:logback-spring.xml
  datasource:
    # 配置数据源类型
    type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    #url: *************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    #url: *******************************************************************************************************************************
    url: jdbc:mysql://*************:3307/DQM?useUnicode=true&characterEncoding=UTF8&allowMultiQueries=true&rewriteBatchedStatements=true
    username: root
    password: 123456
    # 初始化，最小，最大连接数
    initialSize: 5
    minidle: 5
    maxActive: 20
    # 获取数据库连接等待的超时时间
    maxWait: 60000
    # 配置多久进行一次检测，检测需要关闭的空闲连接 单位毫秒
    timeBetweenEvictionRunsMillis: 60000
    validationQuery: SELECT 1
    # 配置监控统计拦截的filters,去掉后，监控界面的sql无法统计
    filters: stat,wall,log4j
    druid:
      # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个，
      #注意: 设置为true后如果要生效,validationQuery参数必须设置为非空字符串
      test-on-borrow: false
      # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
      #注意: 设置为true后如果要生效,validationQuery参数必须设置为非空字符串
      test-while-idle: true
      # 指明是否在归还到池中前进行检验，注意: 设置为true后如果要生效,
      #validationQuery参数必须设置为非空字符串
      test-on-return: false
      # SQL查询,用来验证从连接池取出的连接,在将连接返回给调用者之前.
      #如果指定,则查询必须是一个SQL SELECT并且必须返回至少一行记录
      validation-query: SELECT 1
  quartz:
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: DQM_QRTZ_
            #isClustered: false
            isClustered: true
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    #数据库方式
    job-store-type: jdbc

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: input
      columnFormat: "`%s`"

  configuration:
    map-underscore-to-camel-case: true
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true


workerId: 0
datacenterId: 0

pagehelper:
  helperDialect: mysql
  # 分页合理化参数，默认值为false。当该参数设置为 true 时，
  # pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询。
  reasonable: true
  # 设置不自动分页
  supportMethodsArguments: false
  params: count=countSql

# sa-token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: dqmtoken
  # token有效期，单位s 默认30天(2592000), -1代表永不过期 86400(1天)
  timeout: 604800
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否在初始化配置时打印版本字符画
  isPrint: false
  # 是否打开自动续签 (如果此值为true, 框架会在每次直接或间接调用getLoginId()时进行一次过期检查与续签操作)
  autoRenew: false
  # 配置Sa-Token单独使用的Redis连接 （此处需要和SSO-Server端连接同一个Redis）
  alone-redis:
    # Redis数据库索引
    database: 4
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    #password: Jykj1994
    # 连接超时时间（毫秒）
    timeout: 2000ms
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

#增强功能需要通过配置yml配置文件开启增强,自2.0.6开始
knife4j:
  enable: true
  # 开启Swagger的Basic认证功能,默认是false
  basic:
    enable: true
    # Basic认证用户名
    username: admin
    # Basic认证密码
    password: Jykj1994

wx:
  config:
    state: X
    # 蓬安妇幼
    appId: X
    secret: X
    agentid: X
    qywxRedirectUri: http://weixin.wxlove.xin:8086/ssoapi/wx/qywxLogin
    wxRedirectUri: http://weixin.wxlove.xin:8086/ssoapi/wx/callback
    #二维码相关
    qrCodeAddr: https://open.work.weixin.qq.com
    #企业微信认证地址
    authAddr: https://qyapi.weixin.qq.com

# https://gitee.com/596392912/mica 可以使用 @XssCleanIgnore 注解对方法和类级别进行忽略。如果内置的 xss 清理规则不满足需求，可以自己实现 XssCleaner，注册成 Spring bean 即可。
mica:
  xss:
    enabled: true
    path-patterns: /**
    path-exclude-patterns:
    mode: escape

uploadfile:
  #驱动
  driver:
    win-upload-path: C:/dqm_upload_file
    linux-upload-path: /home/<USER>
    filesize: 10
    filetypes:
      - jar
      - JAR

  image:
    win-upload-path: C:/dqm_upload_file/image
    linux-upload-path: /home/<USER>/image
    #单位（M）
    imageSize: 10
    filetypes:
      - image/jpeg
      - image/jpg
      - image/png
      - image/bmp
      - image/pjpeg
      - image/x-png

#自定义参数
custom:
  #线程池配置
  thread-pool:
    core-size: 20
    max-size: 200
    keep-alive-time: 20  #TimeUnit.SECONDS
    queueCapacity: 500

sms:
  provider: aliyun
  proxy:
    url:
    method-name:
    namespace: