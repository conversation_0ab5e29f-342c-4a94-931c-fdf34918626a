package com.jykj.dqm.metadata.manager;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxg.free.database.DatabaseConfig;
import com.hxg.free.database.Dialect;
import com.hxg.free.database.IntrospectedTable;
import com.hxg.free.database.SimpleDataSource;
import com.hxg.free.utils.DBMetadataUtils;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataStructureDetailMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataStructureDetail;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.service.MetadataStructureDetailService;
import com.jykj.dqm.metadata.service.MetadataStructureInfoService;
import com.jykj.dqm.metadata.service.MetadataTaskInstanceService;
import com.jykj.dqm.utils.AESUtils;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 执行采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 17:48:32
 */
@Slf4j
@Service
public class ExecCollectionTask {
    @Autowired
    MetadataTaskInstanceService metadataTaskInstanceService;

    @Autowired
    MetadataStructureInfoService metadataStructureInfoService;

    @Autowired
    MetadataStructureDetailService metadataStructureDetailService;

    @Autowired
    MetadataStructureDetailMapper metadataStructureDetailMapper;

    /**
     * 执行采集任务，会更新任务状态
     *
     * @param dqmMetadataDatasource MetadataDatasource
     * @param taskGroupId           taskGroupId
     * <AUTHOR>
     */
    @Async("doDQMExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void execCollectionTask(MetadataDatasource dqmMetadataDatasource, String taskGroupId) {
        String taskState = "99";
        String taskResult = "";
        MetadataTaskInstance metadataTaskInstance =
                MetadataTaskInstance.builder()
                        .taskGroupId(taskGroupId)
                        .taskStartDt(new Date())
                        .taskState("00")
                        .dataSourceId(dqmMetadataDatasource.getDataSourceId())
                        .build();
        try {
            metadataTaskInstanceService.save(metadataTaskInstance);
            collectionTask(dqmMetadataDatasource);
            //执行成功
            taskState = "11";
            taskResult = "执行成功！";
        } catch (Throwable e) {
            taskState = "99";
            taskResult = ExceptionUtil.getMessage(e);
            log.error(taskResult, e);
        } finally {
            //更新任务状态
            metadataTaskInstance.setTaskEndDt(new Date());
            metadataTaskInstance.setTaskResult(taskResult);
            metadataTaskInstance.setTaskState(taskState);
            metadataTaskInstanceService.saveOrUpdate(metadataTaskInstance);
        }
    }

    /**
     * 数源配置，同步结构
     *
     * @param dqmMetadataDatasource MetadataDatasource
     * @return 结果
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public R execCollectionTask(MetadataDatasource dqmMetadataDatasource) {
        try {
            collectionTask(dqmMetadataDatasource);
            return RUtil.success();
        } catch (Throwable e) {
            log.error("同步数据库结构失败！", e);
            throw new BusinessException("同步数据库结构失败:" + e.getMessage());
        }
    }

    private void collectionTask(MetadataDatasource dqmMetadataDatasource) throws SQLException {
        Map<String, Dialect> enumMap = EnumUtil.getEnumMap(Dialect.class);
        Dialect dialect = enumMap.get(dqmMetadataDatasource.getDatabaseType().toUpperCase());
        dialect.setClazz(dqmMetadataDatasource.getDatabaseDriver());
        dialect.setSample(dqmMetadataDatasource.getDatabaseUrl());
        String sourceJarPath = null;
        if (StrUtil.isNotBlank(dqmMetadataDatasource.getDriverFiles())) {
            sourceJarPath = SystemUtils.getFilePath() + "/" + dqmMetadataDatasource.getDriverFiles();
        }

        SimpleDataSource dataSource = new SimpleDataSource(
                dialect,
                dqmMetadataDatasource.getDatabaseUrl(),
                dqmMetadataDatasource.getDatabaseUser(),
                " ".equals(AESUtils.AESDeCode(dqmMetadataDatasource.getDatabasePwd())) ? "" : AESUtils.AESDeCode(dqmMetadataDatasource.getDatabasePwd()),
                sourceJarPath
        );
        DBMetadataUtils dbMetadataUtils = new DBMetadataUtils(dataSource);
        DatabaseConfig config;
        if ("SQLSERVER".equalsIgnoreCase(dqmMetadataDatasource.getDatabaseType()) || "ORACLE".equalsIgnoreCase(dqmMetadataDatasource.getDatabaseType())) {
            config = new DatabaseConfig(null, dqmMetadataDatasource.getDatabaseSchema());
        } else {
            config = new DatabaseConfig(dqmMetadataDatasource.getDatabaseSchema(), dqmMetadataDatasource.getDatabaseSchema());
        }
        List<IntrospectedTable> list = dbMetadataUtils.introspectTables(config);
        //List<IntrospectedTable> procedureList = dbMetadataUtils.introspectProcedures(config);
        //list.addAll(procedureList);
        //把采集的结果入库
        MetadataStructureInfo metadataStructureInfo;
        List<MetadataStructureDetail> metadataStructureDetails;
        //删除 1、元数据结构(表/视图)信息表 2、元数据结构详情
        LambdaQueryWrapper<MetadataStructureInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MetadataStructureInfo::getDataSourceId, dqmMetadataDatasource.getDataSourceId());
        metadataStructureInfoService.remove(queryWrapper);
        LambdaQueryWrapper<MetadataStructureDetail> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(MetadataStructureDetail::getDataSourceId, dqmMetadataDatasource.getDataSourceId());
        metadataStructureDetailService.remove(queryWrapper2);
        Map<String, String> configTableMap = new HashMap<>();
        configTableMap.put("schemaName", "schema");
        configTableMap.put("catalogName", "catalog");
        configTableMap.put("tableName", "name");
        configTableMap.put("tableRemark", "remarks");
        configTableMap.put("tableType", "type");

        Map<String, String> configMap = new HashMap<>();
        configMap.put("defaultValue", "defaultvalue");
        configMap.put("columnName", "name");
        configMap.put("columnRemark", "remarks");
        configMap.put("columnType", "type");
        configMap.put("whetherPk", "pk");
        configMap.put("columnLength", "length");
        configMap.put("columnScale", "scale");

        for (IntrospectedTable introspectedTable : list) {
            metadataStructureInfo = MapperUtils.INSTANCE.map(MetadataStructureInfo.class, introspectedTable, configTableMap);
            metadataStructureInfo.setDataSourceId(dqmMetadataDatasource.getDataSourceId());
            metadataStructureInfo.setDatabaseName(dqmMetadataDatasource.getDatabaseName());
            metadataStructureInfo.setSchema(dqmMetadataDatasource.getDatabaseSchema());
            metadataStructureInfoService.save(metadataStructureInfo);
            if (introspectedTable.getBaseColumns().size() == 0) {
                continue;
            }
            metadataStructureDetails = MapperUtils.INSTANCE.mapAsList(MetadataStructureDetail.class, introspectedTable.getBaseColumns(), configMap);
            //添加表名和父表ID
            for (MetadataStructureDetail metadataStructureDetail : metadataStructureDetails) {
                metadataStructureDetail.setStructureInfoId(metadataStructureInfo.getId());
                metadataStructureDetail.setTableName(introspectedTable.getTableName());
                metadataStructureDetail.setDataSourceId(dqmMetadataDatasource.getDataSourceId());
                metadataStructureDetail.setDatabaseName(dqmMetadataDatasource.getDatabaseName());
                metadataStructureDetail.setSchema(dqmMetadataDatasource.getDatabaseSchema());
            }
            metadataStructureDetailService.saveBatch(metadataStructureDetails);
        }
    }
}
