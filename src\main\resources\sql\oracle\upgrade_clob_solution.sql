-- Oracle数据库升级脚本：解决CLOB字段DISTINCT查询问题
-- 方案4：数据库结构优化（添加哈希字段）
-- 执行日期：2025-09-04

-- 1. 添加DETAIL_LINE_HASH字段用于存储DETAIL_LINE的哈希值
ALTER TABLE DQM_DATAQULITY_QSTN_DETAIL ADD DETAIL_LINE_HASH VARCHAR2(64);

-- 2. 添加字段注释
COMMENT ON COLUMN DQM_DATAQULITY_QSTN_DETAIL.DETAIL_LINE_HASH IS 'DETAIL_LINE字段的SHA256哈希值，用于去重查询';

-- 3. 为现有数据生成哈希值（如果表中已有数据）   直接通过接口实现
UPDATE DQM_DATAQULITY_QSTN_DETAIL 
SET DETAIL_LINE_HASH = DBMS_CRYPTO.HASH(UTL_RAW.CAST_TO_RAW(DETAIL_LINE), DBMS_CRYPTO.HASH_SH256)
WHERE DETAIL_LINE IS NOT NULL AND DETAIL_LINE_HASH IS NULL;

-- 4. 创建复合索引提升查询性能
CREATE INDEX IDX_DQM_DETAIL_RULE_HASH ON DQM_DATAQULITY_QSTN_DETAIL(CHECK_RULE_ID, DETAIL_LINE_HASH);

-- 5. 创建单独的哈希字段索引（用于去重查询）
CREATE INDEX IDX_DQM_DETAIL_HASH ON DQM_DATAQULITY_QSTN_DETAIL(DETAIL_LINE_HASH);

-- 6. 提交更改
COMMIT;

-- 验证脚本执行结果
SELECT 
    COUNT(*) as TOTAL_RECORDS,
    COUNT(DETAIL_LINE_HASH) as HASH_RECORDS,
    COUNT(DISTINCT DETAIL_LINE_HASH) as DISTINCT_HASH_RECORDS
FROM DQM_DATAQULITY_QSTN_DETAIL;
