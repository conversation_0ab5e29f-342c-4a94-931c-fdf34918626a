module.log=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory

# 自定义日志打印
#logMessageFormat=com.baomidou.mybatisplus.extension.p6spy.P6SpyLogger
logMessageFormat=com.jykj.dqm.config.P6spySqlFormatConfigure

#日志输出到控制台，解开注释就行了
appender=com.baomidou.mybatisplus.extension.p6spy.StdoutLogger

# 指定输出文件位置
logfile=log/sql.log

# 使用日志系统记录 sql
#appender=com.p6spy.engine.spy.appender.Slf4JLogger

# 设置 p6spy driver 代理
deregisterdrivers=true

# 取消JDBC URL前缀
useprefix=true

# 配置记录 Log 例外,可去掉的结果集有error,info,batch,debug,statement,commit,rollback,result,resultset.
excludecategories=info,debug,result,batch,resultset

# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss

# 实际驱动可多个
#driverlist=oracle.jdbc.driver.OracleDriver

# 是否开启慢SQL记录
outagedetection=true

# 慢SQL记录标准 2 秒
outagedetectioninterval=2

# 是否开启日志过滤 默认false， 这项配置是否生效前提是配置了 include/exclude/sqlexpression
filter=true
# 过滤 Log 时所排除的表名列表，以逗号分隔 默认为空
exclude=DQM_QRTZ_TRIGGERS,DQM_QRTZ_FIRED_TRIGGERS,DQM_QRTZ_LOCKS,DQM_QRTZ_CRON_TRIGGERS,DQM_QRTZ_JOB_DETAILS,DQM_QRTZ_JOB_DETAILS,DQM_QRTZ_PAUSED_TRIGGER_GRPS,DQM_SCHEDULE_JOB_TASK_LOG,DQM_QRTZ_SCHEDULER_STATE
# 过滤 Log 时所包含的表名列表，以逗号分隔 默认为空
#include=
# 过滤 Log 时的 SQL 正则表达式名称  默认为空
#sqlexpression=
