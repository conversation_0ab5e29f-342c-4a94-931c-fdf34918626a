package com.jykj.dqm.quality.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.commondb.DbConfig;
import com.jykj.dqm.config.commondb.DbConnectionPoolUtil;
import com.jykj.dqm.utils.DbQueryNewUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnManagerMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnOperationRecordMapper;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quality.entity.DataqulityQstnDetail;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.quality.entity.DataqulityQstnOperationRecord;
import com.jykj.dqm.quality.entity.QstnRctfctnStusCdEnum;
import com.jykj.dqm.quality.manager.rulesql.CheckRuleTypeEnum;
import com.jykj.dqm.quality.manager.rulesql.CheckSqlFactory;
import com.jykj.dqm.quality.service.impl.DataqulityQstnDetailServiceImpl;
import com.jykj.dqm.utils.HashUtil;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 执行采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 17:48:32
 */
@Slf4j
@Service
public class ExecRuleCheckTask {
    @Autowired
    private DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Autowired
    private MetadataDatasourceMapper metadataDatasourceMapper;

    @Autowired
    private DataqulityQstnManagerMapper dataqulityQstnManagerMapper;

    @Autowired
    private DataqulityQstnDetailServiceImpl dataqulityQstnDetailServiceImpl;

    @Autowired
    private DataqulityQstnOperationRecordMapper dataqulityQstnOperationRecordMapper;

    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Resource(name = "threadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;

    @Async(value = "doDQMExecutor")
    public R exeTask(Map<String, Object> params, DataqualityCheckRule dataqualityCheckRule) {
        String taskState = "99";
        String taskResult = "";
        DataqualityTaskInstance dataqualityTaskInstance =
                DataqualityTaskInstance.builder()
                        .taskGroupId(String.valueOf(params.get("sysCode")))
                        .taskStartDt(new Date())
                        .taskState("00")
                        .checkRuleId(dataqualityCheckRule.getCheckRuleId())
                        .build();
        String totalNum = "0";
        String questionNum = "0";
        try {
            dataqualityTaskInstanceMapper.insert(dataqualityTaskInstance);
            //执行任务
            //1获取数据源连接，通过sysCode+dbName+schema获取
            MetadataDatasource dqmMetadataDatasource = metadataDatasourceMapper.selectOne(
                    new QueryWrapper<MetadataDatasource>()
                            .eq("SYS_CODE", dataqualityCheckRule.getSysCode())
                            .eq("DATABASE_NAME", dataqualityCheckRule.getDbNm())
                            .eq("DATABASE_SCHEMA", dataqualityCheckRule.getCheckSchema())
            );
            //2执行三个执行SQL
            Connection conn = null;
            Statement statement = null;
            ResultSet resultSet = null;

            String questionNumSql = dataqualityCheckRule.getQuestionNmSql();
            String totalNumSql = dataqualityCheckRule.getTotalNmSql();
            String pbSubsidiarySql = dataqualityCheckRule.getPbSubsidiarySql();
            //重新获取执行SQL，主要是及时性需要，时间是根据当前变化
            if (CheckRuleTypeEnum.JSX.getType().equalsIgnoreCase(dataqualityCheckRule.getCheckRuleColumn())) {
                CheckSqlDTO checkSql = CheckSqlFactory.getCheckSql(MapperUtils.INSTANCE.map(DataqualityCheckRuleDTO.class, dataqualityCheckRule));
                questionNumSql = checkSql.getQuestionNmSql();
                totalNumSql = checkSql.getTotalNmSql();
                pbSubsidiarySql = checkSql.getQstDetailFieldSql();
            }

            DataqulityQstnManager dataqulityQstnManager = null;
            try {
                conn = dbQueryUtil.getConnection(dqmMetadataDatasource);
                statement = conn.createStatement();
                resultSet = statement.executeQuery(totalNumSql);
                while (resultSet.next()) {
                    totalNum = resultSet.getString(1);
                    log.info("totalNm:" + totalNum);
                }
                //如果总数为0，不进行问题数查询
                if (StrUtil.isBlank(totalNum) || "0".equals(totalNum)) {
                    log.info("总数为0，不记录");
                    //执行成功
                    taskState = "11";
                    taskResult = "执行成功！";
                    return RUtil.success();
                }

                resultSet = statement.executeQuery(questionNumSql);
                while (resultSet.next()) {
                    questionNum = resultSet.getString(1);
                    log.info("questionNm:" + questionNum);
                }
                //如果问题数为0，不进行明细字段查询
                if (StrUtil.isBlank(questionNum) || "0".equals(questionNum)) {
                    log.info("问题数为0，不记录");
                    //执行成功
                    taskState = "11";
                    taskResult = "执行成功！";
                    return RUtil.success();
                }

                dataqulityQstnManager = DataqulityQstnManager.builder()
                        .dataQltyQstnNm(dataqualityCheckRule.getCheckRuleName())
                        .qstnPrdusSysCd(dataqualityCheckRule.getSysCode())
                        .qstnPrdusSysNm(dataqualityCheckRule.getSysName())
                        .dataQstnNum(Integer.parseInt(questionNum))
                        .dataTotalNum(Integer.parseInt(totalNum))
                        .dbNm(dataqualityCheckRule.getDbNm())
                        .qstnRctfctnStusCd(QstnRctfctnStusCdEnum.PENDING.getCode())
                        .dataQltyRvwRuleNo("" + dataqualityCheckRule.getCheckRuleId())
                        .qstnCheckTime(new Date())
                        .build();
                // 核心逻辑
                if (!"JSX".equals(dataqualityCheckRule.getCheckRuleType())) {
                    TimeInterval timer = DateUtil.timer();
                    //方案一：只保留几条数据
                    //dataList = dealPBSubsidySqlAndLimit(dqmMetadataDatasource, pbSubsidiarySql, 10);
                    //方案二：保留不存在的全部数据（和数据库差集）
                    questionNum = "" + dealPBSubsidySqlAndFilter(dqmMetadataDatasource, pbSubsidiarySql, dataqualityCheckRule.getCheckRuleId(), dataqulityQstnManager);
                    log.info(dataqualityCheckRule.getCheckRuleId() + dataqualityCheckRule.getCheckRuleName() + "过滤重复项花费时间：" + timer.interval() + "毫秒");
                }

                //不记录问题
                //1、如果问题数为0，但是类型不是及时性
                //2、问题数不为0，但是类型是及时性
                if (StrUtil.isBlank(questionNum) || "0".equals(questionNum) || "JSX".equals(dataqualityCheckRule.getCheckRuleType())) {
                    //执行成功
                    taskState = "11";
                    taskResult = "执行成功！";
                    return RUtil.success();
                }

                //如果是及时性需要记录，不会走到dealPBSubsidySqlAndFilter，不会生成问题，非及时性需要更新问题数
                if ("JSX".equals(dataqualityCheckRule.getCheckRuleType())) {
                    dataqulityQstnManagerMapper.insertSelective(dataqulityQstnManager);
                } else {
                    dataqulityQstnManager.setDataQstnNum(Integer.parseInt(questionNum));
                    dataqulityQstnManagerMapper.updateById(dataqulityQstnManager);
                }
                dataqualityTaskInstance.setDataQltyQstnId(dataqulityQstnManager.getPkId() + "");
                //执行成功
                taskState = "11";
                taskResult = "执行成功！";
                return RUtil.success();
            } catch (Throwable exception) {
                //回退
                rollback(dataqulityQstnManager.getPkId());
                throw new BusinessException("执行失败:" + exception.getMessage(), exception);
            } finally {
                DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, dqmMetadataDatasource);
                DbConnectionPoolUtil.returnConnection(dbConfig, conn);
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            taskState = "99";
            taskResult = ExceptionUtil.getMessage(e);
            return RUtil.error(2, "执行失败！");
        } finally {
            //更新任务状态
            dataqualityTaskInstance.setTaskEndDt(new Date());
            dataqualityTaskInstance.setTaskResult(taskResult);
            dataqualityTaskInstance.setTaskState(taskState);
            dataqualityTaskInstance.setDataQstnNum(Integer.parseInt(questionNum));
            dataqualityTaskInstance.setDataTotalNum(Integer.parseInt(totalNum));
            dataqualityTaskInstanceMapper.updateById(dataqualityTaskInstance);
        }
    }

    private void saveDataqulityQstnManager(DataqulityQstnManager dataqulityQstnManager) {
        //记录问题数、总数、问题名称等到问题管理
        dataqulityQstnManagerMapper.insertSelective(dataqulityQstnManager);
        DataqulityQstnOperationRecord dataqulityQstnOperationRecord = DataqulityQstnOperationRecord.builder()
                .operationContent("系统自动生成待处理问题")
                .operationTime(dataqulityQstnManager.getQstnCheckTime())
                .dataQltyQstnId("" + dataqulityQstnManager.getPkId())
                .operationPerson("系统")
                .qstnRctfctnStusCd(QstnRctfctnStusCdEnum.PENDING.getCode())
                .build();
        dataqulityQstnOperationRecordMapper.insert(dataqulityQstnOperationRecord);
    }

    private DataqualityCheckRule getCheckRules(Map<String, Object> params) {
        DataqualityCheckRule dataqualityCheckRule = dataqualityCheckRuleMapper.selectOne(
                new QueryWrapper<DataqualityCheckRule>().eq("CHECK_RULE_ID", params.get("checkRuleId"))
        );
        return dataqualityCheckRule;
    }


    /**
     * 处理问题明细字段，过滤掉已经存在的（会存在数据过大问题）
     *
     * @param dqmMetadataDatasource
     * @param pbSubsidiarySql
     * @param checkRuleId
     * @throws SQLException
     */
    private int dealPBSubsidySqlAndFilter(MetadataDatasource dqmMetadataDatasource, String pbSubsidiarySql, Integer checkRuleId, DataqulityQstnManager dataqulityQstnManager) throws SQLException {
        //获取已经存在的，根据checkRuleId
        QueryWrapper<DataqulityQstnDetail> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DataqulityQstnDetail::getCheckRuleId, checkRuleId);

        // 方案4：使用哈希字段进行去重查询（最优方案）
        long count = getDistinctDetailLineHashCount(checkRuleId);
        //数据量小于200000，不使用BloomFilter
        BloomFilter<String> filter = null;
        //数据库中存在的明细数据
        Set<String> detailLines = null;
        TimeInterval timer = DateUtil.timer();
        boolean useBloomFilter;
        if (count > 200000) {
            useBloomFilter = true;
            filter = getHashBloomFilter(count, checkRuleId);
            log.info(checkRuleId + "创建哈希布隆过滤器花费时间：" + timer.interval() + "毫秒");
        } else {
            useBloomFilter = false;
            // 使用哈希字段进行去重，避免CLOB字段的DISTINCT问题
            QueryWrapper<DataqulityQstnDetail> detailWrapper = new QueryWrapper<>();
            detailWrapper.lambda().eq(DataqulityQstnDetail::getCheckRuleId, checkRuleId);
            detailWrapper.select("DETAIL_LINE_HASH");
            List<DataqulityQstnDetail> dataqulityQstnDetails = dataqulityQstnDetailServiceImpl.list(detailWrapper);
            detailLines = dataqulityQstnDetails.stream()
                .map(DataqulityQstnDetail::getDetailLineHash)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        }
        //一行行比较
        Connection conn = dbQueryUtil.getConnection(dqmMetadataDatasource);
        try {
            Statement stmt = conn.createStatement();
            if ("MYSQL".equalsIgnoreCase(dqmMetadataDatasource.getDatabaseType())) {
                stmt.setFetchSize(Integer.MIN_VALUE);
            }
            ResultSet resultSet = stmt.executeQuery(pbSubsidiarySql);
            ResultSetMetaData pDSmd = resultSet.getMetaData();
            Integer columnCount = pDSmd.getColumnCount();
            String line;
            int index = 0;
            List<String> lineList = new ArrayList<>();
            while (resultSet.next()) {
                JSONObject object = new JSONObject();
                for (int j = 1; j <= columnCount; j++) {
                    String cloumnName = pDSmd.getColumnName(j);
                    object.put(cloumnName, StringUtil.getValue(resultSet.getObject(j)));
                }
                line = object.toJSONString();
                String lineHash = HashUtil.sha256(line);

                //过滤掉已经存在的（使用哈希值进行比较）
                //布隆过滤器 过滤掉已经存在的（有误差）
                if (useBloomFilter && !filter.mightContain(lineHash)) {
                    lineList.add(line);
                    index++;
                }
                //Set集合 过滤掉已经存在的（没有误差）
                if (!useBloomFilter && !detailLines.contains(lineHash)) {
                    lineList.add(line);
                    index++;
                }
                if (index == 1) {
                    saveDataqulityQstnManager(dataqulityQstnManager);
                }
                if (index % 200 == 0) {
                    List<String> tempList = new ArrayList<>(lineList);
                    //保存到数据库
                    CompletableFuture.runAsync(
                            () -> saveDataqulityQstnDetail(tempList, checkRuleId + "", dataqulityQstnManager.getPkId() + "")
                            , threadPoolExecutor);
                    lineList.clear();
                }
            }
            if (index % 200 != 0) {
                List<String> tempList = new ArrayList<>(lineList);
                //保存到数据库
                CompletableFuture.runAsync(
                        () -> saveDataqulityQstnDetail(tempList, checkRuleId + "", dataqulityQstnManager.getPkId() + "")
                        , threadPoolExecutor);
                lineList.clear();
            }
            return index;
        } finally {
            DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, dqmMetadataDatasource);
            DbConnectionPoolUtil.returnConnection(dbConfig, conn);
        }
    }

    private void saveDataqulityQstnDetail(List<String> dataList, String checkRuleId, String dataqulityQstnManagerId) {
        List<DataqulityQstnDetail> list = new ArrayList<>(dataList.size());
        DataqulityQstnDetail dataqulityQstnDetail = null;
        for (String line : dataList) {
            dataqulityQstnDetail = DataqulityQstnDetail.builder()
                    .dataQltyQstnId("" + dataqulityQstnManagerId)
                    .checkRuleId("" + checkRuleId)
                    .detailLine(line)
                    // 哈希值会由Service层自动生成，无需手动设置
                    .build();
            list.add(dataqulityQstnDetail);
        }
        dataqulityQstnDetailServiceImpl.saveBatch(list, 200);
    }

    /**
     * 获取DETAIL_LINE_HASH字段的去重计数
     * 使用哈希字段完美解决Oracle CLOB字段DISTINCT问题
     *
     * @param checkRuleId 规则ID
     * @return 去重后的记录数量
     */
    private long getDistinctDetailLineHashCount(Integer checkRuleId) {
        return dataqulityQstnDetailServiceImpl.getBaseMapper().countDistinctDetailLineHash(checkRuleId);
    }

    /**
     * 创建基于哈希值的布隆过滤器
     *
     * @param count 预期数量
     * @param checkRuleId 规则ID
     * @return 布隆过滤器
     */
    private BloomFilter<String> getHashBloomFilter(long count, Integer checkRuleId) {
        // 预期插入数量
        long capacity = count;
        // 错误比率
        double errorRate = 0.01;
        int size = 40000;
        int totalPage = PageUtil.totalPage(count, size);
        //创建BloomFilter对象，需要传入Funnel对象，预估的元素个数，错误率
        BloomFilter<String> filter = BloomFilter.create(Funnels.stringFunnel(Charset.forName("utf-8")), capacity, errorRate);

        // 创建wrapper用于查询DETAIL_LINE_HASH字段
        QueryWrapper<DataqulityQstnDetail> detailWrapper = new QueryWrapper<>();
        detailWrapper.lambda().eq(DataqulityQstnDetail::getCheckRuleId, checkRuleId);
        detailWrapper.select("DETAIL_LINE_HASH");

        for (int i = 1; i <= totalPage; i++) {
            PageHelper.startPage(i, size, false);
            List<DataqulityQstnDetail> dataqulityQstnDetails = dataqulityQstnDetailServiceImpl.list(detailWrapper);
            PageInfo<DataqulityQstnDetail> pageInfo = new PageInfo<>(dataqulityQstnDetails);
            List<DataqulityQstnDetail> dataqulityQstnDetailList = pageInfo.getList();
            // 提取DETAIL_LINE_HASH字段并添加到布隆过滤器
            dataqulityQstnDetailList.stream()
                .map(DataqulityQstnDetail::getDetailLineHash)
                .filter(Objects::nonNull)
                .forEach(filter::put);
        }
        return filter;
    }

    /**
     * 只取前10条
     *
     * @param dqmMetadataDatasource
     * @param pbSubsidiarySql
     * @param limit
     * @return
     */
    @SneakyThrows
    public List<String> dealPBSubsidySqlAndLimit(MetadataDatasource dqmMetadataDatasource, String pbSubsidiarySql, int limit) {
        Connection conn = dbQueryUtil.getConnection(dqmMetadataDatasource);
        Statement stmt = conn.createStatement();
        if ("MYSQL".equalsIgnoreCase(dqmMetadataDatasource.getDatabaseType())) {
            stmt.setFetchSize(Integer.MIN_VALUE);
        }
        List<String> jsonList = new ArrayList<>();
        ResultSet rs = stmt.executeQuery(pbSubsidiarySql);
        while (rs.next()) {
            if (jsonList.size() > limit) {
                break;
            }
            // 处理每一行数据，例如打印或存储到集合中
            ResultSetMetaData pDSmd = rs.getMetaData();
            Integer columnCount = pDSmd.getColumnCount();
            JSONObject object = new JSONObject();
            for (int j = 1; j <= columnCount; j++) {
                String cloumnName = pDSmd.getColumnName(j);
                object.put(cloumnName, StringUtil.getValue(rs.getObject(j)));
            }
            jsonList.add(object.toJSONString());
        }
        DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, dqmMetadataDatasource);
        DbConnectionPoolUtil.returnConnection(dbConfig, conn);
        return jsonList;
    }

    private void rollback(Integer dataqulityQstnManagerId) {
        if (dataqulityQstnManagerId != null && dataqulityQstnManagerId != 0) {
            dataqulityQstnManagerMapper.deleteById(dataqulityQstnManagerId);
            dataqulityQstnDetailServiceImpl.remove(Wrappers.lambdaQuery(DataqulityQstnDetail.class).eq(DataqulityQstnDetail::getDataQltyQstnId, dataqulityQstnManagerId));
            dataqulityQstnOperationRecordMapper.delete(Wrappers.lambdaQuery(DataqulityQstnOperationRecord.class).eq(DataqulityQstnOperationRecord::getDataQltyQstnId, dataqulityQstnManagerId));
        }
    }

}
