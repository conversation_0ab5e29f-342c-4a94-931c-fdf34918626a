package com.jykj.dqm.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;

/**
 * 哈希工具类
 * 用于生成字符串的哈希值，解决Oracle CLOB字段DISTINCT查询问题
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025-09-04
 */
public class HashUtil {

    /**
     * 生成字符串的SHA256哈希值
     *
     * @param input 输入字符串
     * @return SHA256哈希值（64位十六进制字符串），如果输入为空则返回null
     */
    public static String sha256(String input) {
        if (StrUtil.isBlank(input)) {
            return null;
        }
        return DigestUtil.sha256Hex(input);
    }

    /**
     * 生成字符串的MD5哈希值（备选方案，性能更好但安全性较低）
     *
     * @param input 输入字符串
     * @return MD5哈希值（32位十六进制字符串），如果输入为空则返回null
     */
    public static String md5(String input) {
        if (StrUtil.isBlank(input)) {
            return null;
        }
        return DigestUtil.md5Hex(input);
    }

    /**
     * 验证哈希值是否匹配
     *
     * @param input    原始字符串
     * @param hashValue 哈希值
     * @return 是否匹配
     */
    public static boolean verifySha256(String input, String hashValue) {
        if (StrUtil.isBlank(input) || StrUtil.isBlank(hashValue)) {
            return false;
        }
        return hashValue.equals(sha256(input));
    }

    /**
     * 验证MD5哈希值是否匹配
     *
     * @param input    原始字符串
     * @param hashValue 哈希值
     * @return 是否匹配
     */
    public static boolean verifyMd5(String input, String hashValue) {
        if (StrUtil.isBlank(input) || StrUtil.isBlank(hashValue)) {
            return false;
        }
        return hashValue.equals(md5(input));
    }
}
