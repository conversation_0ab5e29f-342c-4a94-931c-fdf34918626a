package com.jykj.dqm.config;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

import java.text.SimpleDateFormat;

/**
 * p6spy sql格式配置
 * 自定义日志打印配置-SQL格式化输出
 *
 * <AUTHOR>
 * @date 2022/09/02
 */
public class P6spySqlFormatConfigure implements MessageFormattingStrategy {
    private SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String s4) {
        return !"".equals(sql.trim()) ? "SQL语句: " + sql.replaceAll("[\\t\\n\\r]", "") + ";" : "";
    }
}
