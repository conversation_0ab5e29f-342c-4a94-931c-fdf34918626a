package com.jykj.dqm.notice.sms;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.HttpClientUtils;
import com.jykj.dqm.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import jodd.net.HtmlDecoder;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云短信
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/10 15:19:00
 */
@Slf4j
@Service
public class MyAliyunSms {
    @Value("${sms.provider}")
    private String smsProviderTmp;

    @Value("${sms.proxy.url}")
    private String smsProxyUrlTmp;

    @Value("${sms.proxy.method-name}")
    private String methodNameTmp;

    @Value("${sms.proxy.namespace}")
    private String namespaceTmp;

    private static String smsProvider;
    private static String smsProxyUrl;
    private static String methodName;
    private static String namespace;

    @PostConstruct
    public void init() {
        smsProvider = smsProviderTmp;
        smsProxyUrl = smsProxyUrlTmp;
        methodName = methodNameTmp;
        namespace = namespaceTmp;
    }

    /**
     * 根据配置自动选择短信发送方法
     *
     * @param code 验证码
     * @param phoneNumbers 手机号（多个电话号码时，使用,分割）
     * @return 发送结果
     */
    public static R sendSms(String code, String phoneNumbers) {
        try {
            // 从配置中获取短信提供商
            String provider = smsProvider;

            // 如果配置为空，默认使用联麓短信
            if (StrUtil.isBlank(provider)) {
                provider = "aliyun";
            }

            log.info("使用短信提供商: {}", provider);

            // 根据配置选择发送方式
            switch (provider.toLowerCase()) {
                case "aliyun":
                    return sendPin(code, phoneNumbers);
                case "hospital8":
                    return sendPin8Hospital(code, phoneNumbers);
                case "tytx":
                    return sendPin8HospitalByTYTX(code, phoneNumbers);
                case "systemconfig":
                    return sendPinBySystemConfig(code, phoneNumbers);
                case "nczxyy":
                    // 注意：sendByApi 方法参数与其他方法不同，这里假设msg是验证码
                    return sendByApi(code, phoneNumbers);
                case "lianlu":
                    return sendLianLuSms(code, phoneNumbers);
                default:
                    return sendPin(code, phoneNumbers);
            }
        } catch (Exception e) {
            log.error("选择短信发送方法失败: " + e.getMessage(), e);
            return RUtil.error("短信发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送短信，通过配置文件sms.setting
     *
     * @param code
     * @param phoneNumbers
     * @return
     */
    public static R sendPin(String code, String phoneNumbers) {
        Setting setting = new Setting("config/sms.setting");
        // 这里填自己的accessKeyId
        String accessKeyId = setting.getStr("accessKeyId");
        // 这里填自己的accessKey密码
        String secret = setting.getStr("secret");
        // 填自己的模板ID
        String templateCode = setting.getStr("templateCode");
        // 填自己的签名
        String signName = setting.getStr("signName");
        // 因为大部分情况是内网，所以需要用nginx代理出去
        String sendMsgUrl = setting.getStr("sendMsgUrl");
        // 连接阿里云
        DefaultProfile profile = DefaultProfile.getProfile("default", accessKeyId, secret);

        IAcsClient client = new DefaultAcsClient(profile);
        // 构建请求
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setSysMethod(MethodType.POST);
        // 这些内容不要动，阿里的默认配置
        if (StrUtil.isBlank(sendMsgUrl)) {
            sendMsgUrl = "dysmsapi.aliyuncs.com";
        }
        commonRequest.setSysDomain(sendMsgUrl);
        commonRequest.setSysVersion("2017-05-25");

        // 自己的内容,此处 SendSms 为发送验证码
        commonRequest.setSysAction("SendSms");
        commonRequest.putQueryParameter("RegionId", "cn-hangzhou");

        // 自定义的参数(手机号，模板ID，签名 )
        commonRequest.putQueryParameter("PhoneNumbers", phoneNumbers);
        commonRequest.putQueryParameter("TemplateCode", templateCode);
        commonRequest.putQueryParameter("SignName", signName);
        // 构建一个短信的验证码
        commonRequest.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        String reason;
        try {
            CommonResponse response = client.getCommonResponse(commonRequest);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + response.getData());
            JSONObject resultJson = JSONUtil.parseObj(response.getData());
            String resultCode = resultJson.get("Code").toString();
            // 返回请求信息是否成功
            if ("OK".equalsIgnoreCase(resultCode)) {
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + resultJson.get("Message"));
            }
        } catch (ServerException e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        } catch (ClientException e) {
            log.error("发送失败" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    /**
     * 8院特殊发送短信
     *
     * @param code
     * @param phoneNumbers
     * @return
     */
    public static R sendPin8Hospital(String code, String phoneNumbers) {
        String reason;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("CorpID", "LKSDK0003641");
            paramMap.put("Pwd", "jkwqm814");
            paramMap.put("Mobile", phoneNumbers);
            // 使用hutool不用特殊处理
            // String encoding = "GB2312";
            // String encodedContent = URLEncoder.encode(code, encoding);
            // System.out.println(encodedContent);
            // 使用配置的短信接口发送短信验证码
            String msgContent = "您的验证码是：" + code + "，有效期5分钟，请勿泄露给他人。";
            paramMap.put("Content", msgContent);
            // String result =
            // HttpUtil.post("https://sdk1.mb345.com:6789/ws/BatchSend2.aspx", paramMap);
            // String result =
            // HttpUtil.post("http://192.168.100.104:8000/ws/BatchSend2.aspx", paramMap);
            String result = HttpUtil.post(smsProxyUrl, paramMap);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + result);
            // 返回请求信息是否成功
            if (Integer.parseInt(result) > 0) {
                log.info("短信发送成功！" + phoneNumbers);
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + result);
            }
        } catch (Exception e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    /**
     * 统一通讯发送
     *
     * @param code
     * @param phoneNumbers
     * @return
     */
    public static R sendPin8HospitalByTYTX(String code, String phoneNumbers) {
        String reason;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("SYSID", "SSO");
            paramMap.put("CERTIFICATE", "jkwqm814");
            paramMap.put("PHONE", phoneNumbers);
            String msgContent = "您的验证码是：" + code + "，有效期5分钟，请勿泄露给他人。";
            paramMap.put("CONTENT", msgContent);
            paramMap.put("SEND_TIME", DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
            String result = HttpUtil.get(smsProxyUrl, paramMap);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + result);
            // 返回请求信息是否成功
            if (Integer.parseInt(result) > 0) {
                log.info("短信发送成功！" + phoneNumbers);
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + result);
            }
        } catch (Exception e) {
            log.error("发送失败:" + e.getMessage(), e);
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    /**
     * 系统设置发送短信
     *
     * @param code
     * @param phoneNumbers
     * @return
     */
    public static R sendPinBySystemConfig(String code, String phoneNumbers) {
        SysConfig sysConfig = RedisUtil.getSysConfigByName("aliyun.accessKeyId");
        // 这里填自己的accessKeyId
        String accessKeyId = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.secret");
        // 这里填自己的accessKey密码
        String secret = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.templateCode");
        // 填自己的模板ID
        String templateCode = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.signName");
        // 填自己的签名
        String signName = sysConfig.getConfigValue();
        // 连接阿里云
        // DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou",
        // accessKeyId, secret);
        DefaultProfile profile = DefaultProfile.getProfile("default", accessKeyId, secret);

        IAcsClient client = new DefaultAcsClient(profile);

        // 构建请求
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setSysMethod(MethodType.POST);
        // 这些内容不要动，阿里的默认配置,因为大部分情况是内网，所以需要用nginx代理出去
        String sendMsgUrl = RedisUtil.getSysConfigByName("aliyun.sendmsg.nginx.url").getConfigValue();
        if (StrUtil.isBlank(sendMsgUrl)) {
            sendMsgUrl = "dysmsapi.aliyuncs.com";
        }
        commonRequest.setSysDomain(sendMsgUrl);
        commonRequest.setSysVersion("2017-05-25");

        // 自己的内容,此处 SendSms 为发送验证码
        commonRequest.setSysAction("SendSms");
        commonRequest.putQueryParameter("RegionId", "cn-hangzhou");

        // 自定义的参数(手机号，模板ID，签名 )
        commonRequest.putQueryParameter("PhoneNumbers", phoneNumbers);
        commonRequest.putQueryParameter("TemplateCode", templateCode);
        commonRequest.putQueryParameter("SignName", signName);
        // 构建一个短信的验证码
        commonRequest.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        String reason;
        try {
            CommonResponse response = client.getCommonResponse(commonRequest);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + response.getData());
            JSONObject resultJson = JSONUtil.parseObj(response.getData());
            String resultCode = resultJson.get("Code").toString();
            // 返回请求信息是否成功
            if ("OK".equalsIgnoreCase(resultCode)) {
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + resultJson.get("Message"));
            }
        } catch (ServerException e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        } catch (ClientException e) {
            log.error("发送失败" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    /**
     * 南充中心医院短信接口
     *
     * @param phone 手机号
     * @return 发送成功返回验证码，失败抛出异常
     */
    public static R sendByApi(String code, String phone) {
        String reason;
        try {
            String template = RedisUtil.getSysConfigByName("aliyun.nczxyy.template").getConfigValue();

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("api_id", "sendMsg");
            paramMap.put("phone", phone);
            paramMap.put("content", String.format(template, code));

            String sendUrl = RedisUtil.getSysConfigByName("aliyun.nczxyy.url").getConfigValue();
            String response = HttpClientUtils.sendPostWithJson(sendUrl, JSON.toJSONString(paramMap), null);
            if (response != null && JSON.parseObject(response).getJSONObject("result").getInteger("error") == 0) {
                return RUtil.success(code);
            }
            reason = "发送失败:" + JSON.parseObject(response).getJSONObject("result").toJSONString();
        } catch (Exception e) {
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    /**
     * 联麓短信接口
     *
     * @param code 短信验证码
     * @param phoneNumbers 手机号（多个电话号码时，使用,分割）
     * @return 发送结果
     */
    public static R sendLianLuSms(String code, String phoneNumbers) {
        String reason;
        String msgContent = "您的验证码是：" + code + "，有效期5分钟，请勿泄露给他人。";
        try {
            // 构建XML格式的请求内容
            String xmlData = "<DATA>\n" + "\t<SYSID>SSO</SYSID>\n" + "\t<CERTIFICATE>SSO</CERTIFICATE>\n" + "\t<PHONE>"
                + phoneNumbers + "</PHONE>\n" + "\t<CONTENT>" + msgContent + "</CONTENT>\n" + "\t<SEND_TIME>"
                + DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + "</SEND_TIME>\n" + "</DATA>";

            String cdata = "<![CDATA[" + xmlData + "]]>";
            log.info("联麓短信请求消息", cdata);
            // 发送请求
            SoapClient client = SoapClient.create(smsProxyUrl).setMethod(methodName, namespace);
            client.setParam("arg0", cdata, false);
            String result = client.send(true);
            // 解析响应结果
            log.info("联麓短信接口返回信息: {}", result);
            HtmlDecoder htmlEncoder = new HtmlDecoder();
            result = htmlEncoder.decode(result);
            // 解析XML响应
            if (result != null && (result.contains("<TYPE_CODE>AA</TYPE_CODE>"))) {
                log.info("短信发送成功！收件人: {}", phoneNumbers);
                return RUtil.success("发送成功！");
            } else {
                String errorMsg = "发送失败！";
                if (result != null && result.contains("<RESULT>")) {
                    int startIndex = result.indexOf("<RESULT>") + "<RESULT>".length();
                    int endIndex = result.indexOf("</RESULT>");
                    if (startIndex > 0 && endIndex > startIndex) {
                        errorMsg += result.substring(startIndex, endIndex);
                    } else {
                        errorMsg += result;
                    }
                } else {
                    errorMsg += result;
                }
                log.error("短信发送失败！收件人: {}, 错误信息: {}", phoneNumbers, errorMsg);
                return RUtil.error(errorMsg);
            }
        } catch (Exception e) {
            log.error("短信发送失败: " + e.getMessage(), e);
            reason = "发送失败: " + e.getMessage();
        }
        return RUtil.error(reason);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        sendPin8Hospital("11111", "18584880168");
    }
}