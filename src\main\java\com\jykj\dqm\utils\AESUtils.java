/*
 * Copyright (c) 2021-2022 蓬安县妇幼 All Rights Reserved.
 */

package com.jykj.dqm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AES加密工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/4/20 14:20
 */
@Slf4j
public class AESUtils {

    /**
     * base64格式的默认秘钥
     * 也可以每次生成一个随机的秘钥,使用如下代码
     * byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
     * String secret = Base64.encode(key);
     */
    private static final String BASE64_SECRET = "SnlrajE5OTRKeWtqMTk5NA==";

    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 存储解码的值
     */
    private final static Map<String, String> map = new ConcurrentHashMap<>();

    /**
     * 使用aes加密
     *
     * @param content
     * @return
     */
    public static String AESEnCode(String content) {
        //加密完以后是十六进制的
        return aes.encryptHex(content);
    }

    /**
     * 是否是AES加密的CODE
     *
     * @param content
     * @return
     */
    public static boolean isAesEnCode(String content) {
        boolean result = true;
        try {
            aes.decryptStr(content);
        } catch (Exception e) {
            //log.error(e.getMessage(), e);
            result = false;
        }
        return result;
    }

    /**
     * 使用aes算法,进行解密
     *
     * @param ciphertext
     * @return
     */
    public static synchronized String AESDeCode(String ciphertext) {
        if (!isAesEnCode(ciphertext)) {
            return ciphertext;
        }
        if (map.containsKey(ciphertext)) {
            return map.get(ciphertext);
        } else {
            String password = aes.decryptStr(ciphertext);
            map.put(ciphertext, password);
            return password;
        }
    }

    public static void main(String[] args) {
        String string = "Jykj1994";
        String enCode = AESEnCode(string);
        String dnCode = AESDeCode(string);
    }
}
