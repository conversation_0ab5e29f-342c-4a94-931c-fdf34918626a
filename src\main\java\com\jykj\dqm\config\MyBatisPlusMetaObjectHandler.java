package com.jykj.dqm.config;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.jykj.dqm.quality.entity.DataqulityQstnDetail;
import com.jykj.dqm.utils.HashUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * MyBatis Plus 元数据自动填充处理器
 * 用于自动填充实体类的特定字段
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025-09-04
 */
@Slf4j
@Component
public class MyBatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");
        
        // 处理DataqulityQstnDetail的哈希值自动生成
        if (metaObject.getOriginalObject() instanceof DataqulityQstnDetail) {
            DataqulityQstnDetail detail = (DataqulityQstnDetail) metaObject.getOriginalObject();
            
            // 如果DETAIL_LINE有值但DETAIL_LINE_HASH为空，则自动生成哈希值
            if (StrUtil.isNotBlank(detail.getDetailLine()) && StrUtil.isBlank(detail.getDetailLineHash())) {
                String hash = HashUtil.sha256(detail.getDetailLine());
                this.strictInsertFill(metaObject, "detailLineHash", String.class, hash);
                log.debug("自动生成DETAIL_LINE_HASH: {}", hash);
            }
        }
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");
        
        // 处理DataqulityQstnDetail的哈希值自动生成
        if (metaObject.getOriginalObject() instanceof DataqulityQstnDetail) {
            DataqulityQstnDetail detail = (DataqulityQstnDetail) metaObject.getOriginalObject();
            
            // 如果DETAIL_LINE有值但DETAIL_LINE_HASH为空，则自动生成哈希值
            if (StrUtil.isNotBlank(detail.getDetailLine()) && StrUtil.isBlank(detail.getDetailLineHash())) {
                String hash = HashUtil.sha256(detail.getDetailLine());
                this.strictUpdateFill(metaObject, "detailLineHash", String.class, hash);
                log.debug("自动更新DETAIL_LINE_HASH: {}", hash);
            }
            // 如果DETAIL_LINE发生变化，需要重新生成哈希值
            else if (StrUtil.isNotBlank(detail.getDetailLine()) && StrUtil.isNotBlank(detail.getDetailLineHash())) {
                String expectedHash = HashUtil.sha256(detail.getDetailLine());
                if (!expectedHash.equals(detail.getDetailLineHash())) {
                    this.strictUpdateFill(metaObject, "detailLineHash", String.class, expectedHash);
                    log.debug("DETAIL_LINE内容变化，重新生成DETAIL_LINE_HASH: {}", expectedHash);
                }
            }
        }
    }
}
