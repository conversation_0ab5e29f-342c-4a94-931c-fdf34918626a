package com.jykj.dqm.utils;

import cn.hutool.core.map.MapUtil;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Content-type 常用的几种类型
 * application/x-www-form-urlencoded :form表单键值对<K,V>类型 默认类型
 *
 * @version JDK1.8.0_171
 * @date on  2018/12/10 10:40
 * @description V1.0 HttpClient工具类
 */
public class HttpClientUtils {

    /**
     * 获取httppost对象 设置连接超时属性
     * setConnectTimeout：设置连接超时时间，单位毫秒。
     * setConnectionRequestTimeout：设置从connect Manager获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
     * setSocketTimeout：请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
     */
    public static HttpPost getHttpPost(String url) {
        // 创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(20000).setConnectionRequestTimeout(20000)
                .setSocketTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        return httpPost;
    }

    /**
     * @param url 请求地址, map 数据类型, encoding 编码]
     * @return java.lang.String
     * @date 2018/12/10 10:46
     * @descprition pots请求传输 形式数据形式访问
     * @version 1.0
     */
    public static String sendPostDataByMap(String url, Map<String, String> map, String encoding) {
        // 创建httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建post方式请求对象
        HttpPost httpPost = getHttpPost(url);
        // 装填参数
        List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
        if (map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
        }
        // 设置参数到请求对象中
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, encoding));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        // 设置header信息
        // 指定报文头【Content-type】、【User-Agent】
        httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        CloseableHttpResponse response = getPostResponse(httpClient, httpPost);
        return getResult(response, encoding);
    }

    /**
     * @param url 请求笛子, json 请求数据类型, encoding编码
     * @return java.lang.String
     * @date 2018/12/10 10:45
     * @descprition post请求传输json
     * @version 1.0
     * JSON.toJSONString(map) 将map对象转化为json字符串
     */
    public static String sendPostDataByJson(String url, String json, String encoding) {
        // 创建httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建post方式请求对象
        HttpPost httpPost = getHttpPost(url);
        // 设置参数到请求对象中
        StringEntity stringEntity = new StringEntity(json, ContentType.APPLICATION_JSON);
        stringEntity.setContentEncoding(encoding);
        httpPost.setEntity(stringEntity);
        CloseableHttpResponse response = getPostResponse(httpClient, httpPost);
        return getResult(response, encoding);
    }


    public static String sendPostDataByXml(String url, String xml, String encoding) {
        // 创建httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建post方式请求对象
        HttpPost httpPost = getHttpPost(url);
        httpPost.addHeader("Content-Type", "text/xml;charset=UTF-8");
        // 设置参数到请求对象中  text/xml和application/xml的区别
        StringEntity stringEntity = new StringEntity(xml, encoding);
        stringEntity.setContentEncoding(encoding);
        httpPost.setEntity(stringEntity);
        CloseableHttpResponse response = getPostResponse(httpClient, httpPost);
        return getResult(response, encoding);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数是json格式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPostWithJson(String url, String param, Map<String, String> header) throws IOException {
        OutputStreamWriter out;
        BufferedReader in = null;
        String result = "";
        URL realUrl = new URL(url);
        // 打开和URL之间的连接
        HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
        //设置超时时间
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(15000);
        // 设置通用的请求属性
        if (header!=null) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }
        conn.setRequestMethod("POST");
        conn.addRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("accept", "*/*");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent",
                "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        // 发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        // 获取URLConnection对象对应的输出流
        out = new OutputStreamWriter( conn.getOutputStream(),"UTF-8");// utf-8编码
        // 发送请求参数
        out.write(param);

        // flush输出流的缓冲
        out.flush();
        // 定义BufferedReader输入流来读取URL的响应
        in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf8"));
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        if(out!=null){
            out.close();
        }
        if(in!=null){
            in.close();
        }
        return result;
    }

    /**
     * @param url 访问地址, encoding 编码]
     * @return java.lang.String
     * @date 2018/12/10 11:17
     * @descprition get方式请求
     * setConnectTimeout：设置连接超时时间，单位毫秒。
     * setConnectionRequestTimeout：设置从connect Manager获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
     * setSocketTimeout：请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
     * @version 1.0
     */
    public static String sendGetData(String url, String encoding) {
        // 创建httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建get方式请求对象
        HttpGet httpGet = new HttpGet(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000).setConnectionRequestTimeout(1000)
                .setSocketTimeout(5000).build();
        httpGet.setConfig(requestConfig);
        // Content-type设置为application/x-www-form-urlencoded 或者不设置也是可以的(默认为application/x-www-form-urlencoded)
        httpGet.addHeader("Content-type", "application/json");
        // 通过请求对象获取响应对象
        CloseableHttpResponse response = getGetResponse(httpClient, httpGet);
        return getResult(response, encoding);
    }

    /**
     * @param url 访问地址, encoding 编码,headerMap 需要添加在请求头里面的参数]
     * @return java.lang.String
     * @date 2018/12/10 11:17
     * @descprition get方式请求
     * setConnectTimeout：设置连接超时时间，单位毫秒。
     * setConnectionRequestTimeout：设置从connect Manager获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
     * setSocketTimeout：请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
     * @version 1.0
     */
    public static String sendGetDataAddHeader(String url, String encoding, Map<String, String> headerMap) {
        // 创建httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建get方式请求对象
        HttpGet httpGet = new HttpGet(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000).setConnectionRequestTimeout(1000)
                .setSocketTimeout(5000).build();
        httpGet.setConfig(requestConfig);

        if (MapUtil.isNotEmpty(headerMap)) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // Content-type设置为application/x-www-form-urlencoded 或者不设置也是可以的(默认为application/x-www-form-urlencoded)
        httpGet.addHeader("Content-type", "application/json");
        // 通过请求对象获取响应对象
        CloseableHttpResponse response = getGetResponse(httpClient, httpGet);
        return getResult(response, encoding);
    }

    /**
     * @param httpClient , httpPost
     * @return org.apache.http.client.methods.CloseableHttpResponse
     * @date 2018/12/10 11:18
     * @descprition 获取response对象
     * @version 1.0
     */
    public static CloseableHttpResponse getPostResponse(CloseableHttpClient httpClient, HttpPost httpPost) {
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * @param httpClient , httpGET
     * @return org.apache.http.client.methods.CloseableHttpResponse
     * @date 2018/12/10 11:18
     * @descprition 获取response对象
     * @version 1.0
     */
    public static CloseableHttpResponse getGetResponse(CloseableHttpClient httpClient, HttpGet httpGet) {
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGet);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * @param response, encoding]
     * @return java.lang.String
     * @date 2018/12/10 11:18
     * @descprition 获取结果
     * @version 1.0
     */
    public static String getResult(CloseableHttpResponse response, String encoding) {
        String result = "";
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            try {
                result = EntityUtils.toString(response.getEntity(), encoding);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 释放链接
        try {
            response.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
}