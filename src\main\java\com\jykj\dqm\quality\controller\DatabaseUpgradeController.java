package com.jykj.dqm.quality.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.quality.service.DatabaseUpgradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据库升级控制器
 * 用于执行数据库结构升级和数据迁移相关操作
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025-09-04
 */
@Slf4j
@RestController
@RequestMapping("/api/database/upgrade")
@Api(tags = "数据库升级管理")
public class DatabaseUpgradeController {

    @Autowired
    private DatabaseUpgradeService databaseUpgradeService;

    @PostMapping("/generate-hash")
    @ApiOperation("为现有DETAIL_LINE数据生成哈希值")
    public R generateHashForExistingData() {
        try {
            log.info("开始执行哈希值生成任务...");
            int updateCount = databaseUpgradeService.generateHashForExistingData();
            log.info("哈希值生成任务完成，更新了 {} 条记录", updateCount);
            return RUtil.success("哈希值生成完成，更新了 " + updateCount + " 条记录");
        } catch (Exception e) {
            log.error("哈希值生成失败", e);
            return RUtil.error("哈希值生成失败：" + e.getMessage());
        }
    }

    @PostMapping("/verify-hash")
    @ApiOperation("验证哈希值的正确性")
    public R verifyHashValues(
            @ApiParam(value = "抽样验证的记录数量，0表示验证所有记录", example = "1000")
            @RequestParam(defaultValue = "1000") int sampleSize) {
        try {
            log.info("开始执行哈希值验证任务，抽样数量：{}", sampleSize);
            boolean isValid = databaseUpgradeService.verifyHashValues(sampleSize);
            if (isValid) {
                return RUtil.success("哈希值验证通过，所有记录的哈希值都正确");
            } else {
                return RUtil.error("哈希值验证失败，存在不正确的哈希值");
            }
        } catch (Exception e) {
            log.error("哈希值验证失败", e);
            return RUtil.error("哈希值验证失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @ApiOperation("获取数据库升级统计信息")
    public R getUpgradeStatistics() {
        try {
            String statistics = databaseUpgradeService.getUpgradeStatistics();
            return RUtil.success(statistics);
        } catch (Exception e) {
            log.error("获取升级统计信息失败", e);
            return RUtil.error("获取升级统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/help")
    @ApiOperation("获取数据库升级帮助信息")
    public R getUpgradeHelp() {
        String helpInfo = "数据库升级操作指南：\n\n" +
                "1. 首先执行数据库结构升级脚本：\n" +
                "   - Oracle: src/main/resources/sql/oracle/upgrade_clob_solution.sql\n" +
                "   - MySQL: src/main/resources/sql/mysql/upgrade_clob_solution.sql\n\n" +
                "2. 执行哈希值生成：\n" +
                "   POST /api/database/upgrade/generate-hash\n\n" +
                "3. 验证哈希值正确性：\n" +
                "   POST /api/database/upgrade/verify-hash?sampleSize=1000\n\n" +
                "4. 查看升级统计信息：\n" +
                "   GET /api/database/upgrade/statistics\n\n" +
                "自动哈希值生成机制：\n" +
                "- 系统已配置MyBatis Plus自动填充功能\n" +
                "- 新增和更新数据时会自动生成哈希值\n" +
                "- 支持所有MyBatis Plus的CRUD操作\n\n" +
                "注意事项：\n" +
                "- 升级前请备份数据库\n" +
                "- 建议在维护窗口期间执行升级\n" +
                "- 大数据量情况下，哈希值生成可能需要较长时间\n" +
                "- 升级完成后，系统将自动使用新的哈希字段进行去重查询";
        
        return RUtil.success(helpInfo);
    }
}
