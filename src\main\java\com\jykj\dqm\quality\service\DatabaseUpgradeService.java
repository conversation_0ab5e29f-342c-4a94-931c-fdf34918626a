package com.jykj.dqm.quality.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jykj.dqm.quality.dao.DataqulityQstnDetailMapper;
import com.jykj.dqm.quality.entity.DataqulityQstnDetail;
import com.jykj.dqm.quality.service.impl.DataqulityQstnDetailServiceImpl;
import com.jykj.dqm.utils.HashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据库升级服务
 * 用于执行数据库结构升级和数据迁移
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025-09-04
 */
@Slf4j
@Service
public class DatabaseUpgradeService {

    @Autowired
    private DataqulityQstnDetailServiceImpl dataqulityQstnDetailServiceImpl;

    @Autowired
    private DataqulityQstnDetailMapper dataqulityQstnDetailMapper;

    @Autowired
    private DataSourceProperties dataSourceProperties;

    /**
     * 为现有的DETAIL_LINE数据生成哈希值
     * 注意：这个方法应该在数据库结构升级完成后执行
     *
     * @return 更新的记录数量
     */
    @Transactional
    public int generateHashForExistingData() {
        log.info("开始为现有DETAIL_LINE数据生成哈希值...");
        
        // 查询所有没有哈希值的记录
        QueryWrapper<DataqulityQstnDetail> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .isNotNull(DataqulityQstnDetail::getDetailLine)
            .isNull(DataqulityQstnDetail::getDetailLineHash);
        
        List<DataqulityQstnDetail> records = dataqulityQstnDetailServiceImpl.list(wrapper);
        log.info("找到 {} 条需要生成哈希值的记录", records.size());
        
        if (records.isEmpty()) {
            log.info("没有需要更新的记录");
            return 0;
        }
        
        int updateCount = 0;
        int batchSize = 1000;
        
        for (int i = 0; i < records.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, records.size());
            List<DataqulityQstnDetail> batch = records.subList(i, endIndex);
            
            // 为每条记录生成哈希值
            for (DataqulityQstnDetail record : batch) {
                if (StrUtil.isNotBlank(record.getDetailLine())) {
                    record.setDetailLineHash(HashUtil.sha256(record.getDetailLine()));
                }
            }
            
            // 批量更新
            boolean success = dataqulityQstnDetailServiceImpl.updateBatchById(batch);
            if (success) {
                updateCount += batch.size();
                log.info("已更新 {}/{} 条记录", updateCount, records.size());
            } else {
                log.error("批量更新失败，批次起始索引：{}", i);
            }
        }
        
        log.info("哈希值生成完成，共更新 {} 条记录", updateCount);
        return updateCount;
    }

    /**
     * 验证哈希值的正确性
     *
     * @param sampleSize 抽样验证的记录数量，0表示验证所有记录
     * @return 验证结果
     */
    public boolean verifyHashValues(int sampleSize) {
        log.info("开始验证哈希值的正确性...");
        
        QueryWrapper<DataqulityQstnDetail> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .isNotNull(DataqulityQstnDetail::getDetailLine)
            .isNotNull(DataqulityQstnDetail::getDetailLineHash);
        
        if (sampleSize > 0) {
            wrapper.last("LIMIT " + sampleSize);
        }
        
        List<DataqulityQstnDetail> records = dataqulityQstnDetailServiceImpl.list(wrapper);
        log.info("验证 {} 条记录的哈希值", records.size());
        
        int errorCount = 0;
        for (DataqulityQstnDetail record : records) {
            String expectedHash = HashUtil.sha256(record.getDetailLine());
            if (!expectedHash.equals(record.getDetailLineHash())) {
                log.error("记录ID {} 的哈希值不匹配，期望：{}，实际：{}", 
                    record.getDetailId(), expectedHash, record.getDetailLineHash());
                errorCount++;
            }
        }
        
        if (errorCount == 0) {
            log.info("哈希值验证通过，所有记录的哈希值都正确");
            return true;
        } else {
            log.error("哈希值验证失败，发现 {} 条记录的哈希值不正确", errorCount);
            return false;
        }
    }

    /**
     * 获取数据库升级统计信息
     *
     * @return 统计信息
     */
    public String getUpgradeStatistics() {
        // 总记录数
        long totalRecords = dataqulityQstnDetailServiceImpl.count();
        
        // 有DETAIL_LINE的记录数
        QueryWrapper<DataqulityQstnDetail> detailLineWrapper = new QueryWrapper<>();
        detailLineWrapper.lambda().isNotNull(DataqulityQstnDetail::getDetailLine);
        long detailLineRecords = dataqulityQstnDetailServiceImpl.count(detailLineWrapper);
        
        // 有哈希值的记录数
        QueryWrapper<DataqulityQstnDetail> hashWrapper = new QueryWrapper<>();
        hashWrapper.lambda().isNotNull(DataqulityQstnDetail::getDetailLineHash);
        long hashRecords = dataqulityQstnDetailServiceImpl.count(hashWrapper);
        
        // 去重后的哈希值数量
        Long distinctHashRecords = dataqulityQstnDetailMapper.countDistinctDetailLineHash(null);
        if (distinctHashRecords == null) {
            distinctHashRecords = 0L;
        }
        
        String databaseType = dataSourceProperties.getUrl().toUpperCase().contains("ORACLE") ? "Oracle" : "MySQL";
        
        return String.format(
            "数据库升级统计信息：\n" +
            "数据库类型：%s\n" +
            "总记录数：%d\n" +
            "有DETAIL_LINE的记录数：%d\n" +
            "有哈希值的记录数：%d\n" +
            "去重后的哈希值数量：%d\n" +
            "升级完成度：%.2f%%",
            databaseType, totalRecords, detailLineRecords, hashRecords, distinctHashRecords,
            detailLineRecords > 0 ? (hashRecords * 100.0 / detailLineRecords) : 100.0
        );
    }
}
