package com.jykj.dqm.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;

/**
 * 数字工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/27 16:56:08
 */
public class NumberMyUtil extends NumberUtil {
    /**
     * 计算比例，保留6位小数
     *
     * @param totalNum         总数量
     * @param meetConditionNum 满足条件的数量
     * @return 保留6位小数
     * <AUTHOR>
     */
    public static String calculationRatio(Long totalNum, Long meetConditionNum) {
        if (totalNum == 0) {
            return "0";
        }
        return round(meetConditionNum * 1.0 / totalNum, 6).toString();
    }

    /**
     * 计算比例，保留6位小数
     *
     * @param countNum   计算得到的总系数|总系数
     * @param projectNum 满足条件的数量
     * @return 保留6位小数
     * <AUTHOR>
     */
    public static String calculationRatio(double countNum, int projectNum) {
        return round(countNum / projectNum, 6).toString();
    }

    /**
     * 计算比例，保留6位小数
     *
     * @param totalNum         总数量
     * @param meetConditionNum 满足条件的数量
     * @param scale            精度
     * @return 保留6位小数
     * <AUTHOR>
     */
    public static String calculationRatio(Integer totalNum, Integer meetConditionNum, Integer scale) {
        if (totalNum == 0) {
            return "0";
        }
        return round(meetConditionNum * 1.0 / totalNum, 6).toString();
    }

    private static final String[] DIGITS = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] UNITS = new String[]{"", "十", "百", "千", "万", "十万", "百万", "千万", "亿"};

    /**
     * int转中文（一十一显示一十，去掉最后的不需要的零）
     *
     * @param num int数字
     * @return 中文
     * <AUTHOR>
     */
    public static String intToChinese(int num) {
        if (num == 0) {
            return "零";
        }
        StringBuilder result = new StringBuilder();
        int unitIndex = 0;
        boolean lastZero = true; // 上一位是否为零
        while (num > 0) {
            int digit = num % 10;
            if (digit == 0) {
                if (!lastZero && unitIndex < 4) { // 防止出现连续多个零
                    result.insert(0, DIGITS[digit]);
                }
                lastZero = true;
            } else {
                String first = DIGITS[digit];
                //去掉一十一前面的一
                if (unitIndex == 1 && digit == 1) {
                    first = "";
                }
                result.insert(0, first + UNITS[unitIndex]);
                lastZero = false;
            }
            num /= 10;
            unitIndex++;
        }
        return result.toString();
    }

    @Deprecated
    private String intToChinese2(int num) {
        if (num == 0) {
            return "零";
        }
        String[] units = new String[]{"", "十", "百", "千", "万", "十万", "百万", "千万", "亿"};
        String[] digits = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String result = "";
        int unitIndex = 0;
        boolean lastZero = true; // 上一位是否为零
        while (num > 0) {
            int digit = num % 10;
            if (digit == 0) {
                if (!lastZero && unitIndex < 4) { // 防止出现连续多个零
                    result = digits[digit] + result;
                }
                lastZero = true;
            } else {
                String first = digits[digit];
                //去掉一十一前面的一
                if (unitIndex == 1 && digit == 1) {
                    first = "";
                }
                result = first + units[unitIndex] + result;
                lastZero = false;
            }
            num /= 10;
            unitIndex++;
        }
        return result;
    }


    public static void main(String[] args) {
        System.out.println(intToChinese(12));
        //hutool转换
        System.out.println(Convert.numberToChinese(10, false));
    }
}
